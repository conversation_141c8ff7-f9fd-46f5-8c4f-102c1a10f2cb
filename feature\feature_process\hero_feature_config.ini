[feature_config]
is_hero_alive = one_hot:1:eq
location_x = min_max:-60000:60000
location_z = min_max:-60000:60000
level = min_max:1:15
exp = log_normalize:0:12500
money = log_normalize:0:15000
revive_time = min_max:0:60
killCnt = min_max:0:20
deadCnt = min_max:0:20
isInGrass = one_hot:1:eq
hp_rate = min_max:0:1
ep_rate = min_max:0:1
skill_level_0 = min_max:1:6
skill_level_1 = min_max:1:6
skill_level_2 = min_max:1:6
skill_level_3 = min_max:1:6
skill_level_4 = min_max:1:6
skill_level_5 = min_max:1:6
skill_level_6 = min_max:1:6
skill_usable_0 = one_hot:1:eq
skill_usable_1 = one_hot:1:eq
skill_usable_2 = one_hot:1:eq
skill_usable_3 = one_hot:1:eq
skill_usable_4 = one_hot:1:eq
skill_usable_5 = one_hot:1:eq
skill_usable_6 = one_hot:1:eq
skill_cooldown_0 = min_max:0:1
skill_cooldown_1 = min_max:0:1
skill_cooldown_2 = min_max:0:1
skill_cooldown_3 = min_max:0:1
skill_cooldown_4 = min_max:0:1
skill_cooldown_5 = min_max:0:1
skill_cooldown_6 = min_max:0:1

[feature_functions]
is_hero_alive = is_alive
location_x = get_location_x
location_z = get_location_z
level = get_level
exp = get_exp
money = get_money
revive_time = get_revive_time
killCnt = get_kill_cnt
deadCnt = get_dead_cnt
isInGrass = get_is_in_grass
hp_rate = get_hp_rate
ep_rate = get_ep_rate
skill_level_0 = get_skill_level_0
skill_level_1 = get_skill_level_1
skill_level_2 = get_skill_level_2
skill_level_3 = get_skill_level_3
skill_level_4 = get_skill_level_4
skill_level_5 = get_skill_level_5
skill_level_6 = get_skill_level_6
skill_usable_0 = get_skill_usable_0
skill_usable_1 = get_skill_usable_1
skill_usable_2 = get_skill_usable_2
skill_usable_3 = get_skill_usable_3
skill_usable_4 = get_skill_usable_4
skill_usable_5 = get_skill_usable_5
skill_usable_6 = get_skill_usable_6
skill_cooldown_0 = get_skill_cooldown_0
skill_cooldown_1 = get_skill_cooldown_1
skill_cooldown_2 = get_skill_cooldown_2
skill_cooldown_3 = get_skill_cooldown_3
skill_cooldown_4 = get_skill_cooldown_4
skill_cooldown_5 = get_skill_cooldown_5
skill_cooldown_6 = get_skill_cooldown_6
