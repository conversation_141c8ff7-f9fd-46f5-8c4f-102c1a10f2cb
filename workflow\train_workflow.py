#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import os
import time
import random
from agent_ppo.feature.definition import (
    sample_process,
    build_frame,
    FrameCollector,
    NONE_ACTION,
)
from kaiwu_agent.utils.common_func import attached
from agent_ppo.conf.conf import GameConfig
from tools.env_conf_manager import EnvConfManager
from tools.model_pool_utils import get_valid_model_pool
from tools.metrics_utils import get_training_metrics


@attached
def workflow(envs, agents, logger=None, monitor=None, *args, **kwargs):
    # Whether the agent is training, corresponding to do_predicts
    # 智能体是否进行训练
    do_learns = [True, True]
    last_save_model_time = time.time()

    # Create environment configuration manager instance
    # 创建对局配置管理器实例
    env_conf_manager = EnvConfManager(
        config_path="agent_ppo/conf/train_env_conf.toml",
        logger=logger,
    )

    # Create EpisodeRunner instance
    # 创建 EpisodeRunner 实例
    episode_runner = EpisodeRunner(
        env=envs[0],
        agents=agents,
        logger=logger,
        monitor=monitor,
        env_conf_manager=env_conf_manager,
    )

    while True:
        # Run episodes and collect data
        # 运行对局并收集数据
        for g_data in episode_runner.run_episodes():
            for index, (d_learn, agent) in enumerate(zip(do_learns, agents)):
                if d_learn and len(g_data[index]) > 0:
                    # The learner trains in a while true loop, here learn actually sends samples
                    # learner 采用 while true 训练，此处 learn 实际为发送样本
                    agent.learn(g_data[index])
            g_data.clear()

            now = time.time()
            if now - last_save_model_time > GameConfig.MODEL_SAVE_INTERVAL:
                agents[0].save_model()
                last_save_model_time = now


class EpisodeRunner:
    def __init__(self, env, agents, logger, monitor, env_conf_manager):
        self.env = env
        self.agents = agents
        self.logger = logger
        self.monitor = monitor
        self.env_conf_manager = env_conf_manager
        self.agent_num = len(agents)
        self.episode_cnt = 0
        self.predict_success_count = 0
        self.load_model_success_count = 0
        self.last_report_monitor_time = 0

    def run_episodes(self):
        # Single environment process
        # 单局流程
        while True:
            # Retrieving training metrics
            # 获取训练中的指标
            training_metrics = get_training_metrics()
            if training_metrics:
                for key, value in training_metrics.items():
                    if key == "env":
                        for env_key, env_value in value.items():
                            self.logger.info(f"training_metrics {key} {env_key} is {env_value}")
                    else:
                        self.logger.info(f"training_metrics {key} is {value}")

            # Update environment configuration
            # Can use a list of length 2 to pass in the lineup id of the current game
            # 更新对局配置, 可以用长度为2的列表传入当前对局的阵容id
            usr_conf, is_eval, monitor_side = self.env_conf_manager.update_config()
            
            # Random hero selection for selfplay
            # 自对弈随机英雄选择
            if self.env_conf_manager.get_opponent_agent() == "selfplay":
                usr_conf = self._randomize_heroes(usr_conf)

            # Start a new environment
            # 启动新对局，返回初始环境状态
            observation, extra_info = self.env.reset(usr_conf=usr_conf)
            # Disaster recovery
            # 容灾
            if self._handle_disaster_recovery(extra_info):
                break

            # Reset agents
            # 重置智能体
            self.reset_agents(observation)

            # Reset environment frame collector
            # 重置环境帧收集器
            frame_collector = FrameCollector(self.agent_num)

            # Game variables
            # 对局变量
            self.episode_cnt += 1
            frame_no = 0
            reward_sum_list = [0] * self.agent_num
            is_train_test = os.environ.get("is_train_test", "False").lower() == "true"
            self.logger.info(f"Episode {self.episode_cnt} start, usr_conf is {usr_conf}")

            # Reward initialization
            # 回报初始化
            for i, (do_sample, agent) in enumerate(zip(self.do_samples, self.agents)):
                if do_sample:
                    reward = agent.reward_manager.result(observation[i]["frame_state"])
                    observation[i]["reward"] = reward
                    reward_sum_list[i] += reward["reward_sum"]

            while True:
                # Initialize the default actions. If the agent does not make a decision, env.step uses the default action.
                # 初始化默认的actions，如果智能体不进行决策，则env.step使用默认action
                actions = [NONE_ACTION] * self.agent_num

                for index, (do_predict, do_sample, agent) in enumerate(
                    zip(self.do_predicts, self.do_samples, self.agents)
                ):
                    if do_predict:
                        if not is_eval:
                            actions[index] = agent.predict(observation[index])
                        else:
                            actions[index] = agent.exploit(observation[index])
                        self.predict_success_count += 1

                        # Only sample when do_sample=True and is_eval=False
                        # 评估对局数据不采样，不是训练中最新模型产生的数据不采样
                        if not is_eval and do_sample:
                            frame = build_frame(agent, observation[index])
                            frame_collector.save_frame(frame, agent_id=index)

                # Step forward
                # 推进环境到下一帧，得到新的状态
                frame_no, observation, terminated, truncated, extra_info = self.env.step(actions)
                # Disaster recovery
                # 容灾
                if self._handle_disaster_recovery(extra_info):
                    break

                # Reward generation
                # 计算回报，作为当前环境状态observation的一部分
                for i, (do_sample, agent) in enumerate(zip(self.do_samples, self.agents)):
                    if do_sample:
                        reward = agent.reward_manager.result(observation[i]["frame_state"])
                        observation[i]["reward"] = reward
                        reward_sum_list[i] += reward["reward_sum"]

                now = time.time()
                if now - self.last_report_monitor_time >= 60:
                    monitor_data = {
                        "actor_predict_succ_cnt": self.predict_success_count,
                        "actor_load_last_model_succ_cnt": self.load_model_success_count,
                    }
                    self.monitor.put_data({os.getpid(): monitor_data})
                    self.last_report_monitor_time = now

                # Normal end or timeout exit, run train_test will exit early
                # 正常结束或超时退出，运行train_test时会提前退出
                is_gameover = terminated or truncated or (is_train_test and frame_no >= 1000)
                if is_gameover:
                    self.logger.info(
                        f"episode_{self.episode_cnt} terminated in fno_{frame_no}, truncated:{truncated}, eval:{is_eval}, reward_sum:{reward_sum_list[monitor_side]}"
                    )
                    # Reward for saving the last state of the environment
                    # 保存环境最后状态的reward
                    for i, (do_sample, agent) in enumerate(zip(self.do_samples, self.agents)):
                        if not is_eval and do_sample:
                            frame_collector.save_last_frame(
                                agent_id=i,
                                reward=observation[i]["reward"]["reward_sum"],
                            )

                    monitor_data = {}
                    if self.monitor and is_eval:
                        monitor_data["reward"] = round(reward_sum_list[monitor_side], 2)
                        self.monitor.put_data({os.getpid(): monitor_data})

                    # Sample process
                    # 进行样本处理，准备训练
                    if len(frame_collector) > 0 and not is_eval:
                        list_agents_samples = sample_process(frame_collector)
                        yield list_agents_samples
                    break

    def reset_agents(self, observation):
        opponent_agent = self.env_conf_manager.get_opponent_agent()
        monitor_side = self.env_conf_manager.get_monitor_side()
        is_train_test = os.environ.get("is_train_test", "False").lower() == "true"

        # The 'do_predicts' specifies which agents are to perform model predictions.
        # do_predicts 指定哪些智能体要进行模型预测
        # The 'do_samples' specifies which agents are to perform training sampling.
        # do_samples 指定哪些智能体要进行训练采样
        self.do_predicts = [True, True]
        self.do_samples = [True, True]

        # Load model according to the configuration
        # 根据对局配置加载模型
        for i, agent in enumerate(self.agents):
            # Report the latest model in the training camp to the monitor
            # 训练中最新模型所在阵营上报监控
            if i == monitor_side:
                # monitor_side uses the latest model
                # monitor_side 使用最新模型
                agent.load_model(id="latest")
            else:
                if opponent_agent == "common_ai":
                    # common_ai does not need to load a model, no need to predict
                    # 如果对手是 common_ai 则不需要加载模型, 也不需要进行预测
                    self.do_predicts[i] = False
                    self.do_samples[i] = False
                elif opponent_agent == "selfplay":
                    # Training model, "latest" - latest model, "random" - random model from the model pool
                    # 加载训练过的模型，可以选择最新模型，也可以选择随机模型 "latest" - 最新模型, "random" - 模型池中随机模型
                    agent.load_model(id="latest")
                    self.load_model_success_count += 1
                else:
                    # Opponent model, model_id is checked from kaiwu.json
                    # 选择kaiwu.json中设置的对手模型, model_id 即 opponent_agent，必须设置正确否则报错
                    eval_candidate_model = get_valid_model_pool(self.logger)
                    if int(opponent_agent) not in eval_candidate_model:
                        raise Exception(f"opponent_agent model_id {opponent_agent} not in {eval_candidate_model}")
                    else:
                        if is_train_test:
                            # Run train_test, cannot get opponent agent, so replace with latest model
                            # 运行 train_test 时, 无法获取到对手模型，因此将替换为最新模型
                            self.logger.info(f"Run train_test, cannot get opponent agent, so replace with latest model")
                            agent.load_model(id="latest")
                        else:
                            # eval對手[109686, 109599, common_ai] 中隨機抽取
                            eval_opponents = ["109686", "109599", "common_ai"]
                            random_opponent = random.choice(eval_opponents)
                            self.logger.info(f"Randomly selected eval opponent: {random_opponent}")
                            agent.load_opponent_agent(id=random_opponent)
                        self.do_samples[i] = False
            # Reset agent
            # 重置agent
            agent.reset(observation[i])

    def _randomize_heroes(self, usr_conf):
        """Randomize heroes for selfplay training"""
        # 可用的英雄ID列表
        available_heroes = [169, 173, 174]  # 后羿, 李元芳, 虞姬
        
        # 随机选择两个不同的英雄
        import random
        hero1, hero2 = random.sample(available_heroes, 2)
        
        # 更新配置中的英雄ID
        if "lineups" in usr_conf:
            if "blue_camp" in usr_conf["lineups"] and len(usr_conf["lineups"]["blue_camp"]) > 0:
                usr_conf["lineups"]["blue_camp"][0]["hero_id"] = hero1
            if "red_camp" in usr_conf["lineups"] and len(usr_conf["lineups"]["red_camp"]) > 0:
                usr_conf["lineups"]["red_camp"][0]["hero_id"] = hero2
                
        self.logger.info(f"Randomized heroes: Blue={hero1}, Red={hero2}")
        return usr_conf

    def _handle_disaster_recovery(self, extra_info):
        # Handle disaster recovery logic
        # 处理容灾逻辑
        result_code = extra_info.get("result_code", 0)
        result_message = extra_info.get("result_message", "")
        if result_code < 0:
            self.logger.error(f"Env run error, result_code: {result_code}, result_message: {result_message}")
            raise RuntimeError(result_message)
        elif result_code > 0:
            return True
        return False
