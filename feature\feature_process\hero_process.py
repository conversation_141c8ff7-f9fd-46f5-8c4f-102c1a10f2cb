#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from enum import Enum
from agent_ppo.feature.feature_process.feature_normalizer import FeatureNormalizer
import configparser
import os
import math
from collections import OrderedDict


class HeroProcess:
    def __init__(self, camp):
        self.normalizer = FeatureNormalizer()
        self.main_camp = camp
        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"
        self.get_hero_config()
        self.map_feature_to_norm = self.normalizer.parse_config(self.hero_feature_config)
        self.view_dist = 15000
        self.one_unit_feature_num = 33  # 每個英雄33個特徵 (12基礎 + 21技能)
        self.unit_buff_num = 1

    def get_hero_config(self):
        self.config = configparser.ConfigParser()
        self.config.optionxform = str
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, "hero_feature_config.ini")
        self.config.read(config_path)

        # Get normalized configuration
        # 获取归一化的配置
        self.hero_feature_config = []
        for feature, config in self.config["feature_config"].items():
            self.hero_feature_config.append(f"{feature}:{config}")

        # Get feature function configuration
        # 获取特征函数的配置
        self.feature_func_map = {}
        for feature, func_name in self.config["feature_functions"].items():
            if hasattr(self, func_name):
                self.feature_func_map[feature] = getattr(self, func_name)
            else:
                raise ValueError(f"Unsupported function: {func_name}")

    def process_vec_hero(self, frame_state):

        self.generate_hero_info_dict(frame_state)
        self.generate_hero_info_list(frame_state)

        # Generate hero features for our camp
        # 生成我方阵营的英雄特征
        main_camp_hero_vector_feature = self.generate_one_type_hero_feature(self.main_camp_hero_dict, "main_camp")

        return main_camp_hero_vector_feature

    def generate_hero_info_list(self, frame_state):
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()
        for hero in frame_state["hero_states"]:
            if hero["actor_state"]["camp"] == self.main_camp:
                self.main_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                self.main_hero_info = hero
            else:
                self.enemy_camp_hero_dict[hero["actor_state"]["config_id"]] = hero

    def generate_hero_info_dict(self, frame_state):
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()

        # Find our heroes and number them in order
        # 找到我方英雄并按照顺序编号
        for hero in frame_state["npc_states"]:
            if hero["sub_type"] != "ACTOR_SUB_hero" or hero["hp"] <= 0:
                continue
            if hero["camp"] == self.main_camp:
                self.main_camp_hero_dict[hero["runtime_id"]] = hero
        self.main_camp_hero_dict = OrderedDict(sorted(self.main_camp_hero_dict.items()))

        # Find enemy heroes and number them in order
        # 找到敌方英雄并按照顺序编号
        for hero in frame_state["npc_states"]:
            if hero["sub_type"] != "ACTOR_SUB_hero" or hero["hp"] <= 0:
                continue
            if hero["camp"] != self.main_camp:
                self.enemy_camp_hero_dict[hero["runtime_id"]] = hero
        self.enemy_camp_hero_dict = OrderedDict(sorted(self.enemy_camp_hero_dict.items()))

    def generate_one_type_hero_feature(self, one_type_hero_info, camp):
        vector_feature = []
        num_heros_considered = 0
        for hero in one_type_hero_info.values():
            if num_heros_considered >= self.unit_buff_num:
                break

            # Generate each specific feature through feature_func_map
            # 通过 feature_func_map 生成每个具体特征
            for feature_name, feature_func in self.feature_func_map.items():
                value = []
                self.feature_func_map[feature_name](hero, value, feature_name)
                # Normalize the specific features
                # 对具体特征进行正则化
                if feature_name not in self.map_feature_to_norm:
                    assert False
                for k in value:
                    value_vec = []
                    norm_func, *params = self.map_feature_to_norm[feature_name]
                    normalized_value = norm_func(k, *params)
                    if isinstance(normalized_value, list):
                        vector_feature.extend(normalized_value)
                    else:
                        vector_feature.append(normalized_value)
            num_heros_considered += 1

        if num_heros_considered < self.unit_buff_num:
            self.no_hero_feature(vector_feature, num_heros_considered)
        return vector_feature

    def no_hero_feature(self, vector_feature, num_heros_considered):
        for _ in range((self.unit_buff_num - num_heros_considered) * self.one_unit_feature_num):
            vector_feature.append(0)

    def is_alive(self, hero, vector_feature, feature_name):
        value = 0.0
        if hero["actor_state"]["hp"] > 0:
            value = 1.0
        vector_feature.append(value)

    def get_location_x(self, hero, vector_feature, feature_name):
        value = hero["actor_state"]["location"]["x"]
        if self.transform_camp2_to_camp1 and value != 100000:
            value = 0 - value
        vector_feature.append(value)

    def get_location_z(self, hero, vector_feature, feature_name):
        value = hero["actor_state"]["location"]["z"]
        if self.transform_camp2_to_camp1 and value != 100000:
            value = 0 - value
        vector_feature.append(value)
        
    def get_level(self, hero, vector_feature, feature_name):
        """英雄等級"""
        value = hero.get("level", 1)
        vector_feature.append(value)
        
    def get_exp(self, hero, vector_feature, feature_name):
        """英雄經驗值"""
        value = hero.get("exp", 0)
        vector_feature.append(value)
        
    def get_money(self, hero, vector_feature, feature_name):
        """英雄金錢"""
        value = hero.get("money", 0)
        vector_feature.append(value)
        
    def get_revive_time(self, hero, vector_feature, feature_name):
        """復活時間"""
        value = hero.get("revive_time", 0)
        vector_feature.append(value)
        
    def get_kill_cnt(self, hero, vector_feature, feature_name):
        """擊殺數"""
        value = hero.get("killCnt", 0)
        vector_feature.append(value)
        
    def get_dead_cnt(self, hero, vector_feature, feature_name):
        """死亡數"""
        value = hero.get("deadCnt", 0)
        vector_feature.append(value)
        
    def get_is_in_grass(self, hero, vector_feature, feature_name):
        """是否在草叢中"""
        value = 1.0 if hero.get("isInGrass", False) else 0.0
        vector_feature.append(value)
        
    def get_hp_rate(self, hero, vector_feature, feature_name):
        """血量比例"""
        actor_state = hero.get("actor_state", {})
        hp = actor_state.get("hp", 0)
        max_hp = actor_state.get("max_hp", 1)
        value = hp / max_hp if max_hp > 0 else 0.0
        vector_feature.append(value)
        
    def get_ep_rate(self, hero, vector_feature, feature_name):
        """魔法值比例"""
        actor_state = hero.get("actor_state", {})
        values = actor_state.get("values", {})
        ep = values.get("ep", 0)
        max_ep = values.get("max_ep", 1)
        value = ep / max_ep if max_ep > 0 else 0.0
        vector_feature.append(value)
        
    def get_skill_level_0(self, hero, vector_feature, feature_name):
        """技能0等級"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 0:
            value = slot_states[0].get("level", 1)
        else:
            value = 1
        vector_feature.append(value)
        
    def get_skill_level_1(self, hero, vector_feature, feature_name):
        """技能1等級"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 1:
            value = slot_states[1].get("level", 1)
        else:
            value = 1
        vector_feature.append(value)
        
    def get_skill_level_2(self, hero, vector_feature, feature_name):
        """技能2等級"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 2:
            value = slot_states[2].get("level", 1)
        else:
            value = 1
        vector_feature.append(value)
        
    def get_skill_level_3(self, hero, vector_feature, feature_name):
        """技能3等級"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 3:
            value = slot_states[3].get("level", 1)
        else:
            value = 1
        vector_feature.append(value)
        
    def get_skill_level_4(self, hero, vector_feature, feature_name):
        """技能4等級"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 4:
            value = slot_states[4].get("level", 1)
        else:
            value = 1
        vector_feature.append(value)
        
    def get_skill_level_5(self, hero, vector_feature, feature_name):
        """技能5等級"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 5:
            value = slot_states[5].get("level", 1)
        else:
            value = 1
        vector_feature.append(value)
        
    def get_skill_level_6(self, hero, vector_feature, feature_name):
        """技能6等級"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 6:
            value = slot_states[6].get("level", 1)
        else:
            value = 1
        vector_feature.append(value)
        
    def get_skill_usable_0(self, hero, vector_feature, feature_name):
        """技能0是否可用"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 0:
            value = 1.0 if slot_states[0].get("usable", False) else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_usable_1(self, hero, vector_feature, feature_name):
        """技能1是否可用"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 1:
            value = 1.0 if slot_states[1].get("usable", False) else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_usable_2(self, hero, vector_feature, feature_name):
        """技能2是否可用"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 2:
            value = 1.0 if slot_states[2].get("usable", False) else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_usable_3(self, hero, vector_feature, feature_name):
        """技能3是否可用"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 3:
            value = 1.0 if slot_states[3].get("usable", False) else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_usable_4(self, hero, vector_feature, feature_name):
        """技能4是否可用"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 4:
            value = 1.0 if slot_states[4].get("usable", False) else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_usable_5(self, hero, vector_feature, feature_name):
        """技能5是否可用"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 5:
            value = 1.0 if slot_states[5].get("usable", False) else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_usable_6(self, hero, vector_feature, feature_name):
        """技能6是否可用"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 6:
            value = 1.0 if slot_states[6].get("usable", False) else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_cooldown_0(self, hero, vector_feature, feature_name):
        """技能0冷卻時間比例"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 0:
            cooldown = slot_states[0].get("cooldown", 0)
            cooldown_max = slot_states[0].get("cooldown_max", 1)
            value = cooldown / cooldown_max if cooldown_max > 0 else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_cooldown_1(self, hero, vector_feature, feature_name):
        """技能1冷卻時間比例"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 1:
            cooldown = slot_states[1].get("cooldown", 0)
            cooldown_max = slot_states[1].get("cooldown_max", 1)
            value = cooldown / cooldown_max if cooldown_max > 0 else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_cooldown_2(self, hero, vector_feature, feature_name):
        """技能2冷卻時間比例"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 2:
            cooldown = slot_states[2].get("cooldown", 0)
            cooldown_max = slot_states[2].get("cooldown_max", 1)
            value = cooldown / cooldown_max if cooldown_max > 0 else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_cooldown_3(self, hero, vector_feature, feature_name):
        """技能3冷卻時間比例"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 3:
            cooldown = slot_states[3].get("cooldown", 0)
            cooldown_max = slot_states[3].get("cooldown_max", 1)
            value = cooldown / cooldown_max if cooldown_max > 0 else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_cooldown_4(self, hero, vector_feature, feature_name):
        """技能4冷卻時間比例"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 4:
            cooldown = slot_states[4].get("cooldown", 0)
            cooldown_max = slot_states[4].get("cooldown_max", 1)
            value = cooldown / cooldown_max if cooldown_max > 0 else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_cooldown_5(self, hero, vector_feature, feature_name):
        """技能5冷卻時間比例"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 5:
            cooldown = slot_states[5].get("cooldown", 0)
            cooldown_max = slot_states[5].get("cooldown_max", 1)
            value = cooldown / cooldown_max if cooldown_max > 0 else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
        
    def get_skill_cooldown_6(self, hero, vector_feature, feature_name):
        """技能6冷卻時間比例"""
        skill_state = hero.get("skill_state", {})
        slot_states = skill_state.get("slot_states", [])
        if len(slot_states) > 6:
            cooldown = slot_states[6].get("cooldown", 0)
            cooldown_max = slot_states[6].get("cooldown_max", 1)
            value = cooldown / cooldown_max if cooldown_max > 0 else 0.0
        else:
            value = 0.0
        vector_feature.append(value)
