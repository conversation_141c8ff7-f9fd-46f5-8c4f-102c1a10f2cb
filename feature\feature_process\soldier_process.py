#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from enum import Enum
from agent_ppo.feature.feature_process.feature_normalizer import FeatureNormalizer
import configparser
import os
import math
from collections import OrderedDict


class SoldierProcess:
    def __init__(self, camp):
        self.normalizer = FeatureNormalizer()
        self.main_camp = camp
        
        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
        self.main_camp_soldiers = []
        self.enemy_camp_soldiers = []
        
        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"
        self.get_soldier_config()
        self.map_feature_to_norm = self.normalizer.parse_config(self.soldier_feature_config)
        self.view_dist = 15000
        self.one_unit_feature_num = 5  # 每個小兵5個特徵 (包含1維camp)
        self.top_k_soldiers = 4  # 選擇距離最近的4個小兵
        
    def get_soldier_config(self):
        self.config = configparser.ConfigParser()
        self.config.optionxform = str
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, "soldier_feature_config.ini")
        self.config.read(config_path)
        
        # Get normalized configuration
        # 獲取歸一化的配置
        self.soldier_feature_config = []
        for feature, config in self.config["feature_config"].items():
            self.soldier_feature_config.append(f"{feature}:{config}")
            
        # Get feature function configuration
        # 獲取特徵函數的配置
        self.feature_func_map = {}
        for feature, func_name in self.config["feature_functions"].items():
            if hasattr(self, func_name):
                self.feature_func_map[feature] = getattr(self, func_name)
            else:
                raise ValueError(f"Unsupported function: {func_name}")
                
    def process_vec_soldier(self, frame_state):
        self.generate_hero_info_list(frame_state)
        self.generate_soldier_info_list(frame_state)
        
        local_vector_feature = []
        
        # 生成敵方小兵特徵（距離最近的top 4）
        enemy_soldier_feature = self.generate_top_k_soldier_feature(self.enemy_camp_soldiers, "enemy")
        local_vector_feature.extend(enemy_soldier_feature)
        
        # 生成我方小兵特徵（距離最近的top 4）
        main_soldier_feature = self.generate_top_k_soldier_feature(self.main_camp_soldiers, "main")
        local_vector_feature.extend(main_soldier_feature)
        
        return local_vector_feature
        
    def generate_hero_info_list(self, frame_state):
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()
        for hero in frame_state["hero_states"]:
            if hero["actor_state"]["camp"] == self.main_camp:
                self.main_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                self.main_hero_info = hero
            else:
                self.enemy_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                
    def generate_soldier_info_list(self, frame_state):
        self.main_camp_soldiers.clear()
        self.enemy_camp_soldiers.clear()
        
        # 從 npc_states 中篩選出所有小兵
        for unit in frame_state["npc_states"]:
            if unit["sub_type"] == "ACTOR_SUB_SOLDIER" and unit["hp"] > 0:
                if unit["camp"] == self.main_camp:
                    self.main_camp_soldiers.append(unit)
                else:
                    self.enemy_camp_soldiers.append(unit)
                    
    def cal_distance_to_hero(self, unit):
        """計算單位與我方英雄的距離"""
        hero_location = self.main_hero_info["actor_state"]["location"]
        unit_location = unit["location"]
        
        dx = hero_location["x"] - unit_location["x"]
        dz = hero_location["z"] - unit_location["z"]
        distance = math.sqrt(dx * dx + dz * dz)
        return distance
        
    def generate_top_k_soldier_feature(self, soldiers, camp_type):
        """生成距離最近的top K個小兵的特徵"""
        vector_feature = []
        
        if not soldiers:
            # 如果沒有小兵，用0填充所有位置
            self.no_soldier_feature(vector_feature, 0)
            return vector_feature
            
        # 計算每個小兵與英雄的距離並排序
        soldiers_with_distance = []
        for soldier in soldiers:
            distance = self.cal_distance_to_hero(soldier)
            soldiers_with_distance.append((soldier, distance))
            
        # 按距離排序（從近到遠）
        soldiers_with_distance.sort(key=lambda x: x[1])
        
        # 選取前top_k_soldiers個
        selected_soldiers = soldiers_with_distance[:self.top_k_soldiers]
        
        # 為每個選中的小兵生成特徵
        for soldier, distance in selected_soldiers:
            for feature_name, feature_func in self.feature_func_map.items():
                value = []
                feature_func(soldier, value, feature_name)
                
                # 歸一化特徵
                if feature_name not in self.map_feature_to_norm:
                    assert False, f"Feature {feature_name} not found in normalization config"
                    
                for k in value:
                    norm_func, *params = self.map_feature_to_norm[feature_name]
                    normalized_value = norm_func(k, *params)
                    if isinstance(normalized_value, list):
                        vector_feature.extend(normalized_value)
                    else:
                        vector_feature.append(normalized_value)
                        
        # 如果小兵數量不足top_k_soldiers，用0填充
        actual_count = len(selected_soldiers)
        if actual_count < self.top_k_soldiers:
            self.no_soldier_feature(vector_feature, actual_count)
            
        return vector_feature
        
    def no_soldier_feature(self, vector_feature, current_count):
        """用0填充缺失的小兵特徵位置"""
        missing_count = self.top_k_soldiers - current_count
        for _ in range(missing_count * self.one_unit_feature_num):
            vector_feature.append(0.0)
            
    def is_alive(self, soldier, vector_feature, feature_name):
        """小兵是否存活"""
        value = 1.0 if soldier["hp"] > 0 else 0.0
        vector_feature.append(value)
        
    def get_hp_rate(self, soldier, vector_feature, feature_name):
        """小兵血量比例"""
        value = 0.0
        if soldier["max_hp"] > 0:
            value = soldier["hp"] / soldier["max_hp"]
        vector_feature.append(value)
        
    def relative_location_x(self, soldier, vector_feature, feature_name):
        """相對於我方英雄的X座標"""
        soldier_x = soldier["location"]["x"]
        hero_x = self.main_hero_info["actor_state"]["location"]["x"]
        x_diff = soldier_x - hero_x
        
        if self.transform_camp2_to_camp1 and soldier_x != 100000:
            x_diff = -x_diff
            
        # 歸一化到[-1, 1]範圍
        value = x_diff / 30000.0
        vector_feature.append(value)
        
    def relative_location_z(self, soldier, vector_feature, feature_name):
        """相對於我方英雄的Z座標"""
        soldier_z = soldier["location"]["z"]
        hero_z = self.main_hero_info["actor_state"]["location"]["z"]
        z_diff = soldier_z - hero_z
        
        if self.transform_camp2_to_camp1 and soldier_z != 100000:
            z_diff = -z_diff
            
        # 歸一化到[-1, 1]範圍
        value = z_diff / 30000.0
        vector_feature.append(value)
        
    def get_camp(self, soldier, vector_feature, feature_name):
        """小兵陣營信息 - 1維相對編碼: 1=友方, 0=敵方"""
        value = 1.0 if soldier["camp"] == self.main_camp else 0.0
        vector_feature.append(value)
