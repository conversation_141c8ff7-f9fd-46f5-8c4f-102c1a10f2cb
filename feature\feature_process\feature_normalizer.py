#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


class FeatureNormalizer:
    def __init__(self):
        pass

    def one_hot(self, value, value_list, compare_method):
        # one hot encoding
        # one hot 编码
        if compare_method != "eq":
            raise ValueError("Unsupported compare method: " + compare_method)
        return [1 if value == v else 0 for v in value_list]

    def min_max(self, value, min_value, max_value):
        # Normalize the value based on min and max values
        # 根据最大值和最小值归一化
        if value <= min_value:
            return 0
        elif value >= max_value:
            return 1
        else:
            return (value - min_value) / (max_value - min_value)
            
    def log_normalize(self, value, min_value, max_value):
        # Logarithmic normalization
        # 对数正规化
        import math
        
        # 避免對數運算的負數或零值
        if value <= 0:
            return 0.0
            
        # 對數正規化公式: log(1 + value) / log(1 + max_value)
        log_value = math.log(1 + value)
        log_max = math.log(1 + max_value)
        
        # 歸一化到 [0, 1] 範圍
        normalized_value = log_value / log_max if log_max > 0 else 0.0
        
        return normalized_value

    def parse_config(self, config_list):
        config_dict = {}
        for config_str in config_list:
            parts = config_str.split(":")
            feature_name = parts[0]
            method = parts[1]
            if method == "one_hot":
                values = list(map(int, parts[2:-1]))
                compare_method = parts[-1]
                config_dict[feature_name] = (self.one_hot, values, compare_method)
            elif method == "min_max":
                min_value = int(parts[2])
                max_value = int(parts[3])
                config_dict[feature_name] = (self.min_max, min_value, max_value)
            elif method == "log_normalize":
                min_value = int(parts[2])
                max_value = int(parts[3])
                config_dict[feature_name] = (self.log_normalize, min_value, max_value)
            else:
                raise ValueError("Unsupported method: " + method)
        return config_dict
